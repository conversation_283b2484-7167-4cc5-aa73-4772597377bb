#!/usr/bin/env python3
"""
Test script to verify the new endpoints are working correctly.
This script tests the roles CRUD operations and user assignment endpoints.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from src.main import app

def test_roles_endpoints():
    """Test the roles CRUD endpoints"""
    client = TestClient(app)
    
    print("Testing Roles Endpoints...")
    
    # Test GET /v1/roles (should return empty list initially)
    response = client.get("/v1/roles")
    print(f"GET /v1/roles - Status: {response.status_code}")
    if response.status_code == 200:
        print("✅ Roles list endpoint working")
    else:
        print(f"❌ Roles list endpoint failed: {response.text}")
    
    # Test POST /v1/roles (create a role)
    role_data = {
        "code": "TEST_ROLE",
        "name": "Test Role",
        "description": "A test role for verification"
    }
    response = client.post("/v1/roles", json=role_data)
    print(f"POST /v1/roles - Status: {response.status_code}")
    if response.status_code == 201:
        print("✅ Role creation endpoint working")
        role_id = response.json()["id"]
        
        # Test GET /v1/roles/{id}
        response = client.get(f"/v1/roles/{role_id}")
        print(f"GET /v1/roles/{role_id} - Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Role get by ID endpoint working")
        else:
            print(f"❌ Role get by ID endpoint failed: {response.text}")
        
        # Test PUT /v1/roles/{id}
        update_data = {"description": "Updated test role description"}
        response = client.put(f"/v1/roles/{role_id}", json=update_data)
        print(f"PUT /v1/roles/{role_id} - Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Role update endpoint working")
        else:
            print(f"❌ Role update endpoint failed: {response.text}")
        
        return role_id
    else:
        print(f"❌ Role creation endpoint failed: {response.text}")
        return None

def test_user_assignment_endpoints():
    """Test the user assignment endpoints"""
    client = TestClient(app)
    
    print("\nTesting User Assignment Endpoints...")
    
    # Test user-department assignment endpoint structure
    response = client.post("/v1/users/assign-department", json={
        "user_id": "00000000-0000-0000-0000-000000000000",
        "department_id": "00000000-0000-0000-0000-000000000000"
    })
    print(f"POST /v1/users/assign-department - Status: {response.status_code}")
    # We expect this to fail with 500 because the UUIDs don't exist, but the endpoint should be reachable
    if response.status_code in [404, 500]:
        print("✅ User-department assignment endpoint reachable")
    else:
        print(f"❌ User-department assignment endpoint issue: {response.text}")
    
    # Test user-role assignment endpoint structure
    response = client.post("/v1/users/assign-role", json={
        "user_id": "00000000-0000-0000-0000-000000000000",
        "role_id": "00000000-0000-0000-0000-000000000000"
    })
    print(f"POST /v1/users/assign-role - Status: {response.status_code}")
    # We expect this to fail with 500 because the UUIDs don't exist, but the endpoint should be reachable
    if response.status_code in [404, 500]:
        print("✅ User-role assignment endpoint reachable")
    else:
        print(f"❌ User-role assignment endpoint issue: {response.text}")

def test_users_endpoints():
    """Test the users endpoints"""
    client = TestClient(app)
    
    print("\nTesting Users Endpoints...")
    
    # Test GET /v1/users
    response = client.get("/v1/users")
    print(f"GET /v1/users - Status: {response.status_code}")
    if response.status_code == 200:
        print("✅ Users list endpoint working")
    else:
        print(f"❌ Users list endpoint failed: {response.text}")

def main():
    """Main test function"""
    print("🚀 Starting endpoint tests...\n")
    
    try:
        # Test roles endpoints
        role_id = test_roles_endpoints()
        
        # Test user assignment endpoints
        test_user_assignment_endpoints()
        
        # Test users endpoints
        test_users_endpoints()
        
        print("\n✅ All endpoint tests completed!")
        
        # Clean up - delete the test role if created
        if role_id:
            client = TestClient(app)
            response = client.delete(f"/v1/roles/{role_id}", params={"delete_reason": "Test cleanup"})
            if response.status_code == 204:
                print("✅ Test role cleaned up successfully")
            else:
                print(f"⚠️ Could not clean up test role: {response.text}")
                
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
