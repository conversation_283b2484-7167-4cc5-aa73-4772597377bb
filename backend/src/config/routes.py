from fastapi import FastAPI

from src.modules.users.users_router import router as user_routes
from src.modules.activities.activity_router import \
				router as activities_router_v1
from src.modules.auth.auth_router import router as auth_router_v1
from src.modules.complaint.complaint_router import \
				router as complaint_router_v1
from src.modules.organization.organization_router import \
				router as organization_router_v1
from src.modules.settings.settings_router import router as settings_router_v1
from src.modules.departments.departments_router import router as departments_router_v1
from src.modules.roles.roles_router import router as roles_router_v1

v1_prefix = "/v1"

def configure_routes(app: FastAPI) -> None:
	app.include_router(auth_router_v1, prefix="/v1/auth")
	app.include_router(settings_router_v1, prefix="/v1/settings")
	app.include_router(user_routes, prefix="/v1/users")
	app.include_router(complaint_router_v1, prefix="/v1/complaints")
	app.include_router(organization_router_v1, prefix=f'{v1_prefix}/organizations')
	app.include_router(activities_router_v1, prefix="/v1/activities")
	app.include_router(departments_router_v1, prefix="/v1/departments")
	app.include_router(roles_router_v1, prefix="/v1/roles")
