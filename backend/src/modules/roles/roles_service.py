from typing import Any
from uuid import UUID
from sqlalchemy.exc import IntegrityError
from src.core.exceptions.api import ApiException
from src.modules.roles.roles_schema import RoleResponse, RoleFilters, RoleCreate, RoleUpdate
from src.core.base.application_repository import ApplicationRepository
from src.config.db.models.role import Role


class RolesService(ApplicationRepository):

    def __init__(self):
        super().__init__()

    def find_roles(self, filters: RoleFilters):
        """
        Find roles based on filters.

        Args:
            filters (RoleFilters): Filters to apply.

        Returns:
            Query: SQLAlchemy query object for roles matching the criteria.
        """
        role_filters = ['code', 'name', 'description']

        try:
            rfilters: dict = {k: v for k, v in filters if v is not None and k in role_filters}

            query = self.db.query(Role)
            
            if rfilters:
                # Handle partial matching for name and description
                for field in ["name", "description"]:
                    if field in rfilters:
                        filter_value = rfilters.pop(field)
                        query = query.filter(
                            getattr(Role, field).like(f"%{filter_value}%")
                        )

                # Handle exact matching for remaining filters
                query = query.filter_by(**rfilters)
            
            return query
        except Exception as e:
            self.logger.error(f"Error finding roles: {e}")
            raise e

    def create_role(self, role_data: RoleCreate) -> RoleResponse:
        """
        Create a new role.

        Args:
            role_data (RoleCreate): Role data to create.

        Returns:
            RoleResponse: Created role data.
        """
        try:
            # Check if role with same code already exists
            existing_role = self.db.query(Role).filter(
                Role.code == role_data.code
            ).first()
            
            if existing_role:
                raise ApiException('Role with this code already exists')

            role = Role(
                code=role_data.code,
                name=role_data.name,
                description=role_data.description
            )
            
            self.db.add(role)
            self.db.commit()
            self.db.refresh(role)
            
            return RoleResponse.model_validate(role)
        except IntegrityError as e:
            self.db.rollback()
            self.logger.error(f"Integrity error creating role: {e}")
            raise ApiException('Role with this code already exists')
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error creating role: {e}")
            raise e

    def get_role_by_id(self, role_id: UUID) -> RoleResponse:
        """
        Get a role by ID.

        Args:
            role_id (UUID): Role ID.

        Returns:
            RoleResponse: Role data.
        """
        try:
            role = self.db.query(Role).filter(
                Role.id == role_id
            ).first()
            
            if not role:
                raise ApiException('Role not found')
            
            return RoleResponse.model_validate(role)
        except Exception as e:
            self.logger.error(f"Error getting role: {e}")
            raise e

    def update_role(self, role_id: UUID, updates: RoleUpdate) -> RoleResponse:
        """
        Update a role.

        Args:
            role_id (UUID): Role ID to update.
            updates (RoleUpdate): Updates to apply.

        Returns:
            RoleResponse: Updated role data.
        """
        try:
            role = self.db.query(Role).filter(
                Role.id == role_id
            ).first()
            
            if not role:
                raise ApiException('Role not found')

            # Check for conflicts with code if it's being updated
            update_dict = {k: v for k, v in updates if v is not None}
            
            if 'code' in update_dict:
                existing_code = self.db.query(Role).filter(
                    Role.id != role_id,
                    Role.code == update_dict['code']
                ).first()
                if existing_code:
                    raise ApiException('Role with this code already exists')

            # Apply updates
            for k, v in update_dict.items():
                setattr(role, k, v)
            
            self.db.commit()
            self.db.refresh(role)
            
            return RoleResponse.model_validate(role)
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error updating role: {e}")
            raise e

    def delete_role(self, role_id: UUID, delete_reason: str) -> None:
        """
        Soft delete a role by setting voided=True.

        Args:
            role_id (UUID): Role ID to delete.
            delete_reason (str): Reason for deletion.
        """
        try:
            role = self.db.query(Role).filter(
                Role.id == role_id
            ).first()
            
            if not role:
                raise ApiException('Role not found')

            # Check if role has active users
            if role.user_roles:
                # Check if there are any non-voided user roles
                from src.config.db.models.user_role import UserRole
                active_user_roles = self.db.query(UserRole).filter(
                    UserRole.role_id == role_id,
                    UserRole.voided == False
                ).first()
                
                if active_user_roles:
                    raise ApiException('Cannot delete role with active users. Please reassign users first.')

            role.voided = True
            role.voided_reason = delete_reason
            self.db.commit()
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error deleting role: {e}")
            raise e
