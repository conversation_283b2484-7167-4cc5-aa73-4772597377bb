from typing import Any
from uuid import UUID
from sqlalchemy.exc import IntegrityError
from src.core.exceptions.api import ApiException
from src.modules.departments.departments_schema import DepartmentResponse, DepartmentFilters, DepartmentCreate
from src.core.base.application_repository import ApplicationRepository
from src.config.db.models.department import Department


class DepartmentsService(ApplicationRepository):

    def __init__(self):
        super().__init__()

    def find_departments(self, filters: DepartmentFilters):
        """
        Find departments based on filters.

        Args:
            filters (DepartmentFilters): Filters to apply.

        Returns:
            Query: SQLAlchemy query object for departments matching the criteria.
        """
        department_filters = ['name', 'code', 'description']

        try:
            dfilters: dict = {k: v for k, v in filters if v is not None and k in department_filters}

            query = self.db.query(Department)
            
            if dfilters:
                # Handle partial matching for name and description
                for field in ["name", "description"]:
                    if field in dfilters:
                        filter_value = dfilters.pop(field)
                        query = query.filter(
                            getattr(Department, field).like(f"%{filter_value}%")
                        )

                # Handle exact matching for remaining filters
                query = query.filter_by(**dfilters)
            
            return query
        except Exception as e:
            self.logger.error(f"Error finding departments: {e}")
            raise e

    def create_department(self, department_data: DepartmentCreate) -> DepartmentResponse:
        """
        Create a new department.

        Args:
            department_data (DepartmentCreate): Department data to create.

        Returns:
            DepartmentResponse: Created department data.
        """
        try:
            # Check if department with same name or code already exists
            existing_dept = self.db.query(Department).filter(
                (Department.name == department_data.name) | 
                (Department.code == department_data.code)
            ).first()
            
            if existing_dept:
                if existing_dept.name == department_data.name:
                    raise ApiException('Department with this name already exists')
                if existing_dept.code == department_data.code:
                    raise ApiException('Department with this code already exists')

            department = Department(
                name=department_data.name,
                code=department_data.code,
                description=department_data.description
            )
            
            self.db.add(department)
            self.db.commit()
            self.db.refresh(department)
            
            return DepartmentResponse.model_validate(department)
        except IntegrityError as e:
            self.db.rollback()
            self.logger.error(f"Integrity error creating department: {e}")
            raise ApiException('Department with this name or code already exists')
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error creating department: {e}")
            raise e

    def get_department_by_id(self, department_id: UUID) -> DepartmentResponse:
        """
        Get a department by ID.

        Args:
            department_id (UUID): Department ID.

        Returns:
            DepartmentResponse: Department data.
        """
        try:
            department = self.db.query(Department).filter(
                Department.id == department_id
            ).first()
            
            if not department:
                raise ApiException('Department not found')
            
            return DepartmentResponse.model_validate(department)
        except Exception as e:
            self.logger.error(f"Error getting department: {e}")
            raise e

    def update_department(self, department_id: UUID, updates: Any) -> DepartmentResponse:
        """
        Update a department.

        Args:
            department_id (UUID): Department ID to update.
            updates (Any): Updates to apply.

        Returns:
            DepartmentResponse: Updated department data.
        """
        try:
            department = self.db.query(Department).filter(
                Department.id == department_id
            ).first()
            
            if not department:
                raise ApiException('Department not found')

            # Check for conflicts with name or code if they're being updated
            update_dict = {k: v for k, v in updates if v is not None}
            
            if 'name' in update_dict or 'code' in update_dict:
                conflict_query = self.db.query(Department).filter(
                    Department.id != department_id
                )
                
                if 'name' in update_dict:
                    existing_name = conflict_query.filter(Department.name == update_dict['name']).first()
                    if existing_name:
                        raise ApiException('Department with this name already exists')
                
                if 'code' in update_dict:
                    existing_code = conflict_query.filter(Department.code == update_dict['code']).first()
                    if existing_code:
                        raise ApiException('Department with this code already exists')

            # Apply updates
            for k, v in update_dict.items():
                setattr(department, k, v)
            
            self.db.commit()
            self.db.refresh(department)
            
            return DepartmentResponse.model_validate(department)
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error updating department: {e}")
            raise e

    def delete_department(self, department_id: UUID, delete_reason: str) -> None:
        """
        Soft delete a department by setting voided=True.

        Args:
            department_id (UUID): Department ID to delete.
            voided_reason (str): Reason for voided.
        """
        try:
            department = self.db.query(Department).filter(
                Department.id == department_id
            ).first()
            
            if not department:
                raise ApiException('Department not found')

            # Check if department has active users
            if department.user_departments:
                active_users = [ud for ud in department.user_departments if not ud.voided]
                if active_users:
                    raise ApiException('Cannot delete department with active users. Please reassign users first.')

            department.voided = True
            department.voided_reason = delete_reason
            self.db.commit()
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error deleting department: {e}")
            raise e
