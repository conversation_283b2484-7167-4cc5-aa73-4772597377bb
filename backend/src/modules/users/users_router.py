from fastapi import APIRouter, status
from fastapi_pagination import Page
from src.modules.users.users_schema import (
    UserResponse, UserDepartmentAssignRequest, UserDepartmentResponse,
    UserRoleAssignRequest, UserRoleResponse
)
from src.modules.users import users_controller as controller


router = APIRouter(tags=["users"])

router.add_api_route(
    path="",
    endpoint=controller.index,
    name='Get and Search For Users',
    methods=["GET"],
    status_code=status.HTTP_200_OK,
    response_model=Page[UserResponse]
)

router.add_api_route(
    path="",
    name='Create User',
    endpoint=controller.create,
    methods=["POST"],
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
)

router.add_api_route(
    name="Edit User Attributes",
    path="/{id}",
    endpoint=controller.update,
    methods=["PUT"],
    response_model=UserResponse,
    status_code=status.HTTP_200_OK,
)

router.add_api_route(
    path="/{id}",
    endpoint=controller.delete,
    methods=["DELETE"],
    name='Delete A User',
    status_code=status.HTTP_204_NO_CONTENT,
)

# User-Department Assignment Routes
router.add_api_route(
    path="/assign-department",
    endpoint=controller.assign_user_to_department,
    methods=["POST"],
    name='Assign User to Department',
    response_model=UserDepartmentResponse,
    status_code=status.HTTP_201_CREATED,
)

router.add_api_route(
    path="/{user_id}/unassign-department",
    endpoint=controller.unassign_user_from_department,
    methods=["DELETE"],
    name='Unassign User from Department',
    status_code=status.HTTP_204_NO_CONTENT,
)

# User-Role Assignment Routes
router.add_api_route(
    path="/assign-role",
    endpoint=controller.assign_role_to_user,
    methods=["POST"],
    name='Assign Role to User',
    response_model=UserRoleResponse,
    status_code=status.HTTP_201_CREATED,
)

router.add_api_route(
    path="/{user_id}/unassign-role",
    endpoint=controller.unassign_role_from_user,
    methods=["DELETE"],
    name='Unassign Role from User',
    status_code=status.HTTP_204_NO_CONTENT,
)
