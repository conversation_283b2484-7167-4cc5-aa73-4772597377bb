from uuid import UUID
from fastapi import Depends, HTTPException, status
from fastapi_pagination.ext.sqlalchemy import paginate
from src.modules.auth.auth_schema import UserCreate
from src.modules.auth.auth_service import AuthService
from src.modules.users.users_schema import (
    UserResponse, UserFilters, UserUpdate,
    UserDepartmentAssignRequest, UserDepartmentResponse,
    UserRoleAssignRequest, UserRoleResponse
)
from src.modules.users.users_service import UsersService

service = UsersService()
auth = AuthService()

def index(filters: UserFilters = Depends(UserFilters)) -> list[UserResponse]:
    try:
        return paginate(service.find_users(filters))
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

def create(user: UserCreate) -> UserResponse:
    try:
        return auth.register_user(user)
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def update(id, updates: UserUpdate) -> UserResponse:
    try:
        return service.update_user(user_id=id, updates=updates)
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def delete(user_id: UUID):
    try:
        return service.delete_user(user_id)
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def assign_user_to_department(request: UserDepartmentAssignRequest) -> UserDepartmentResponse:
    """
    Assign a user to a department.

    Args:
        request (UserDepartmentAssignRequest): Assignment request data.

    Returns:
        UserDepartmentResponse: Assignment response data.
    """
    try:
        return service.assign_user_to_department(request)
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def unassign_user_from_department(user_id: UUID):
    """
    Unassign a user from their current department.

    Args:
        user_id (UUID): User ID to unassign.
    """
    try:
        service.unassign_user_from_department(user_id)
        return {"message": "User unassigned from department successfully"}
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def assign_role_to_user(request: UserRoleAssignRequest) -> UserRoleResponse:
    """
    Assign a role to a user.

    Args:
        request (UserRoleAssignRequest): Assignment request data.

    Returns:
        UserRoleResponse: Assignment response data.
    """
    try:
        return service.assign_role_to_user(request)
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def unassign_role_from_user(user_id: UUID):
    """
    Unassign a role from a user.

    Args:
        user_id (UUID): User ID to unassign role from.
    """
    try:
        service.unassign_role_from_user(user_id)
        return {"message": "Role unassigned from user successfully"}
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
