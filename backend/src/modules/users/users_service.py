from uuid import UUID
from sqlalchemy.orm import joinedload
from src.core.exceptions.api import ApiException
from src.modules.users.users_schema import (
    UserResponse, UserFilters, UserUpdate,
    UserDepartmentAssignRequest, UserDepartmentResponse,
    UserRoleAssignRequest, UserRoleResponse
)
from src.core.base.application_repository import ApplicationRepository
from src.config.db.models.user import User
from src.config.db.models.user_department import UserDepartment
from src.config.db.models.user_role import UserRole
from src.config.db.models.department import Department
from src.config.db.models.role import Role

class UsersService(ApplicationRepository):

    def __init__(self):
        super().__init__()

    def _user_to_response(self, user: User) -> UserResponse:
        """
        Convert User model to UserResponse with department and role information.

        Args:
            user (User): User model instance.

        Returns:
            UserResponse: User response with department and role info.
        """
        from src.modules.users.users_schema import DepartmentInfo, RoleInfo

        # Get department info if user has department assignment
        department_info = None
        if user.user_department and not user.user_department.voided and user.user_department.department:
            dept = user.user_department.department
            department_info = DepartmentInfo(
                id=dept.id,
                name=dept.name,
                code=dept.code,
                description=dept.description
            )

        # Get role info if user has role assignment
        role_info = None
        if user.user_role and not user.user_role.voided and user.user_role.role:
            role = user.user_role.role
            role_info = RoleInfo(
                id=role.id,
                name=role.name,
                code=role.code,
                description=role.description
            )

        # Create user response
        user_data = UserResponse.model_validate(user)
        user_data.department = department_info
        user_data.role = role_info

        return user_data

    def find_users(self, filters: UserFilters) -> list:
        """
          Find users based on filters.

          Args:
            filters (dict): Dictionary of filters to apply.

          Returns:
            list: List of users matching the criteria.
        """
        user_filters = ['first_name', 'middle_name', 'last_name', 'email', 'is_external']

        account_filters = ['handle', 'type', 'status']

        try:
            ufilters: dict = {k: v for k,v in filters if v is not None and k in user_filters}
            afilters: dict = {k: v for k, v in filters if v is not None and k in account_filters}

            query = self.db.query(User).options(
                joinedload(User.account),
                joinedload(User.user_department).joinedload(UserDepartment.department),
                joinedload(User.user_role).joinedload(UserRole.role)
            )

            if ufilters:
                for field in ["first_name", "middle_name", "last_name"]:
                    if field in ufilters:
                        filter_value = ufilters.pop(field)
                        query = query.filter(
                            getattr(User, field).like(f"{filter_value}%")
                        )

                query = query.filter_by(**ufilters)
            if afilters:
                query = query.join(User.account).filter_by(**afilters)
            return query
        except Exception as e:
            self.logger.error(f"Error finding users: {e}")
            raise e

    def update_user(self, user_id: UUID, updates: UserUpdate) -> UserResponse:
        try:                        
            user = self.db.query(User).filter_by(id=user_id).first()
            if user:
                for k, v in updates:
                    if v is not None: 
                      setattr(user, k, v)
            
            account = user.account
            if account:
              for k, v in updates:
                if v is not None:
                  setattr(account, k, v)
            else:
                raise ApiException('user not found')
            self.db.commit()
            self.db.refresh(user)
            return self._user_to_response(user)
        except Exception as e:
            self.logger.error(f"Error updating user: {e}")
            raise e

    def delete_user(self, user_id) -> None:
      try:
          user = self.db.query(User).filter_by(id=user_id).first()
          if user.account.handle == 'root':
            return ApiException('Cannot delete superuser account')

          if user:
              self.db.delete(user)
              self.db.commit()
              return
          else:
              raise ApiException('user not found')
      except Exception as e:
          self.logger.error(f"Error deleting user: {e}")
          raise e

    def assign_user_to_department(self, request: UserDepartmentAssignRequest) -> UserDepartmentResponse:
        """
        Assign a user to a department.

        Args:
            request (UserDepartmentAssignRequest): Assignment request data.

        Returns:
            UserDepartmentResponse: Assignment response data.
        """
        try:
            # Validate user exists
            user = self.db.query(User).filter(User.id == request.user_id).first()
            if not user:
                raise ApiException('User not found')

            # Validate department exists
            department = self.db.query(Department).filter(Department.id == request.department_id).first()
            if not department:
                raise ApiException('Department not found')

            # Check if user already has a department assignment
            existing_assignment = self.db.query(UserDepartment).filter(
                UserDepartment.user_id == request.user_id,
                UserDepartment.voided == False
            ).first()

            if existing_assignment:
                raise ApiException('User is already assigned to a department. Please unassign first.')

            # Create new assignment
            assignment = UserDepartment(
                user_id=request.user_id,
                department_id=request.department_id
            )

            self.db.add(assignment)
            self.db.commit()
            self.db.refresh(assignment)

            return UserDepartmentResponse.model_validate(assignment)

        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error assigning user to department: {e}")
            raise e

    def unassign_user_from_department(self, user_id: UUID) -> None:
        """
        Unassign a user from their current department.

        Args:
            user_id (UUID): User ID to unassign.
        """
        try:
            # Find current assignment
            assignment = self.db.query(UserDepartment).filter(
                UserDepartment.user_id == user_id,
                UserDepartment.voided == False
            ).first()

            if not assignment:
                raise ApiException('User is not assigned to any department')

            # Soft delete the assignment
            assignment.voided = True
            assignment.voided_reason = "User unassigned from department"

            self.db.commit()

        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error unassigning user from department: {e}")
            raise e

    def assign_role_to_user(self, request: UserRoleAssignRequest) -> UserRoleResponse:
        """
        Assign a role to a user.

        Args:
            request (UserRoleAssignRequest): Assignment request data.

        Returns:
            UserRoleResponse: Assignment response data.
        """
        try:
            # Validate user exists
            user = self.db.query(User).filter(User.id == request.user_id).first()
            if not user:
                raise ApiException('User not found')

            # Validate role exists
            role = self.db.query(Role).filter(Role.id == request.role_id).first()
            if not role:
                raise ApiException('Role not found')

            # Check if user already has a role assignment
            existing_assignment = self.db.query(UserRole).filter(
                UserRole.user_id == request.user_id,
                UserRole.voided == False
            ).first()

            if existing_assignment:
                raise ApiException('User already has a role assigned. Please unassign first.')

            # Create new assignment
            assignment = UserRole(
                user_id=request.user_id,
                role_id=request.role_id
            )

            self.db.add(assignment)
            self.db.commit()
            self.db.refresh(assignment)

            return UserRoleResponse.model_validate(assignment)

        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error assigning role to user: {e}")
            raise e

    def unassign_role_from_user(self, user_id: UUID) -> None:
        """
        Unassign a role from a user.

        Args:
            user_id (UUID): User ID to unassign role from.
        """
        try:
            # Find current assignment
            assignment = self.db.query(UserRole).filter(
                UserRole.user_id == user_id,
                UserRole.voided == False
            ).first()

            if not assignment:
                raise ApiException('User does not have any role assigned')

            # Soft delete the assignment
            assignment.voided = True
            assignment.voided_reason = "Role unassigned from user"

            self.db.commit()

        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error unassigning role from user: {e}")
            raise e